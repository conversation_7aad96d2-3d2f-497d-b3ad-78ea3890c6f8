<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DateRangePicker Localization Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/fr.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/de.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/es.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            margin: 5px;
            padding: 8px 15px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>DateRangePicker Localization Test</h1>
    
    <div class="test-section">
        <h2>Test Locale-Aware Date Formatting</h2>
        <p>This test verifies that moment.js formats dates correctly based on the current locale.</p>
        
        <button onclick="testLocale('en')">Test English</button>
        <button onclick="testLocale('fr')">Test French</button>
        <button onclick="testLocale('de')">Test German</button>
        <button onclick="testLocale('es')">Test Spanish</button>
        
        <div id="locale-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Test Date Parsing and Formatting</h2>
        <p>This test simulates the DateRangePicker behavior with different locales.</p>
        
        <button onclick="testDateRangePicker('en')">Test English DateRange</button>
        <button onclick="testDateRangePicker('fr')">Test French DateRange</button>
        <button onclick="testDateRangePicker('de')">Test German DateRange</button>
        <button onclick="testDateRangePicker('es')">Test Spanish DateRange</button>
        
        <div id="daterange-test-results"></div>
    </div>

    <script>
        function testLocale(locale) {
            // Set moment locale
            moment.locale(locale);
            
            const testDate = moment('2024-01-15');
            const results = document.getElementById('locale-test-results');
            
            // Test different formats
            const formats = {
                'll': testDate.format('ll'),  // This is what we use in the fix
                'DD MMM YYYY': testDate.format('DD MMM YYYY'),  // Old hardcoded format
                'L': testDate.format('L'),
                'LL': testDate.format('LL')
            };
            
            let html = `<div class="test-result">
                <h3>Locale: ${locale}</h3>
                <p><strong>Current moment locale:</strong> ${moment.locale()}</p>`;
            
            for (const [format, result] of Object.entries(formats)) {
                html += `<p><strong>${format}:</strong> ${result}</p>`;
            }
            
            html += '</div>';
            results.innerHTML = html;
        }

        function testDateRangePicker(locale) {
            // Set moment locale
            moment.locale(locale);
            
            const startDate = moment('2024-01-15');
            const endDate = moment('2024-01-20');
            const results = document.getElementById('daterange-test-results');
            
            // Simulate the calendar plugin behavior (after fix)
            const formattedRange = startDate.format('ll') + ' - ' + endDate.format('ll');
            
            // Simulate old behavior (before fix)
            const oldFormattedRange = startDate.format('DD MMM YYYY') + ' - ' + endDate.format('DD MMM YYYY');
            
            let html = `<div class="test-result">
                <h3>DateRangePicker Test - Locale: ${locale}</h3>
                <p><strong>Current moment locale:</strong> ${moment.locale()}</p>
                <p><strong>New format (ll):</strong> ${formattedRange}</p>
                <p><strong>Old format (DD MMM YYYY):</strong> ${oldFormattedRange}</p>`;
            
            // Check if the formats are different (indicating localization is working)
            if (locale !== 'en' && formattedRange !== oldFormattedRange) {
                html += '<p class="success">✓ Localization is working correctly!</p>';
            } else if (locale === 'en') {
                html += '<p class="success">✓ English format is correct</p>';
            } else {
                html += '<p class="error">✗ Localization may not be working</p>';
            }
            
            html += '</div>';
            results.innerHTML = html;
        }

        // Test parsing dates with different formats
        function testDateParsing() {
            const testCases = [
                { input: '15 Jan 2024', format: 'DD MMM YYYY' },
                { input: '15/01/2024', format: 'DD/MM/YYYY' },
                { input: '2024-01-15', format: 'YYYY-MM-DD' }
            ];
            
            console.log('Testing date parsing:');
            testCases.forEach(test => {
                const parsed = moment(test.input, test.format);
                console.log(`Input: ${test.input}, Format: ${test.format}, Valid: ${parsed.isValid()}, Result: ${parsed.format('ll')}`);
            });
        }

        // Run initial test
        testLocale('en');
        testDateRangePicker('en');
        testDateParsing();
    </script>
</body>
</html>
