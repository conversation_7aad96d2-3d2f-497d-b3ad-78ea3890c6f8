/* eslint-disable */
"use strict";

(function($) {
  function DatePicker($el, options) {
    $.fn.modal.prototype.constructor.Constructor.DEFAULTS.backdrop = 'static';
    $.fn.modal.prototype.constructor.Constructor.DEFAULTS.keyboard =  false;
    moment = options.moment || moment

    if (options === 'destroy') {
      var _id = $el.attr('id').split('_')[1]
      $('#' + _id).remove()
      $('#datepicker_' + _id).remove()
      $('[data-target="#' + _id + '"]').remove()
      return
    }

    var id = options.id
    var self = this;
    var defaultDates = $el.val();
    var startDate = new Date();
    var endDate = new Date();

    {
      startDate.setHours(0,0,0,0);
      endDate.setHours(0,0,0,0);
    }

    if (defaultDates && defaultDates.split("-").length) {
      startDate = moment(defaultDates.split("-")[0], options.format).toDate();
      endDate = moment(defaultDates.split("-")[1], options.format).toDate();
    }

    var $modal,
      isModal = false;
    var $body = $('<div class="row"><div class="col-xs-12"><div class="datepicker_from_to" id="datepicker_' + id + '"></div></div></div>')
    var $ranges;

    if (options.customRange && options.hasRange) {
      var ranges = options.customRange;
      $ranges = $('<div class="row"><div class="col-xs-12" style="border:1px solid #eee;"><div class="text-center"></div></div></div>')

      for (var i = 0; i < ranges.length; i++) {
        var className = ranges[i].label.toLowerCase().split(' ').join('-');
        $ranges.find('div.text-center').append('<button class="' + className + ' btn btn-xs btn-link">' + ranges[i].label + '</button>')
      }
    } else if (options.hasRange) {
      $ranges = $('<div class="row"><div class="col-xs-12" style="border:1px solid #eee;"><div class="text-center"><button class="yesterday btn btn-xs btn-link">Yesterday</button><button class="today btn btn-xs btn-link">Today</button>' + '<button class="term btn btn-xs btn-link">Term</button><button class="term-to-date btn btn-xs btn-link">Term to Date</button></div></div></div>')
    }

    if (options.clockpicker) {
      var inputStartTime = '<div class="form-group form-group-material">\n    <label class="control-label">Start Time</label>\n    <div class="input-group start_time">\n        <input readonly type="text" value="' + moment(startDate).format('HH:mm') + '" class="form-control">\n        <span class="input-group-addon cursor-pointer">\n            <i class="icon-alarm"></i>\n        </span>\n    </div>\n</div>'
      var inputEndTime = '<div class="form-group form-group-material">\n    <label class="control-label">End Time</label>\n    <div class="input-group end_time">\n        <input readonly type="text" value="' + moment(endDate).format('HH:mm') + '" class="form-control">\n        <span class="input-group-addon cursor-pointer">\n            <i class="icon-alarm"></i>\n        </span>\n    </div>\n</div>'
      var rangeStr = '<div class="col-md-8 col-md-offset-2 col-sm-12 col-xs-12 col-sm-offset-0 mt-20">\n    <div class="row">\n        <div class="col-sm-6">' + inputStartTime + '</div>\n        <div class="col-xs-12 visible-xs"></div>\n        <div class="col-sm-6">' + inputEndTime + '</div>\n    </div>\n</div>'

      if ($ranges) {
        $ranges.append(rangeStr);
      } else {
        $ranges = $(rangeStr)
      }
    }

    $body.append($ranges);

    if ($el.parents('.modal-body').length) {
      isModal = true
      $modal = $('<div class="modal-body pb-10 include-calendar-' + id + '" style="display:none;"></div>')
      var $modalFooter = $('<div class="modal-footer include-calendar-' + id + '" style="display:none;">\n    <button type="button" class="btn btn-sm btn-link dpicker-cancel-btn legitRipple">' + options.cancelButtonText + '</button>\n    <button type="button" class="btn btn-sm dpicker-apply-btn btn-primary btn-rounded legitRipple">' + options.applyButtonText + '</button>\n</div>')
      $modal.append($body)
      $modal.insertAfter($el.parents('.modal-body'))
      $modalFooter.insertAfter($el.parents('.modal').find('.modal-body.include-calendar-' + id))
    } else {
      $modal = $('<div id="' + id + '" class="modal fade">\n    <div class="modal-dialog">\n        <div class="modal-content">\n            <div class="modal-body pb-10"></div>\n            <div class="modal-footer">\n                <small class="error"/>\n                <button type="button" class="btn btn-sm btn-link dpicker-cancel-btn legitRipple">' + options.cancelButtonText + '</button>\n                <button type="button" class="btn btn-sm dpicker-apply-btn btn-primary btn-rounded legitRipple">' + options.applyButtonText + '</button>\n            </div>\n        </div>\n    </div>\n</div>')
      $modal.find('.modal-body').html($body)
      $('#modal-portal').append($modal)
    }

    var _picker;

    if (options.view == "months") {
      var currentY = new Date().getFullYear();
      self.currentY = currentY;
      var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      var $wrapper = $('<div class="datepicker_from_to"><div class="pickmeup pmu-flat pmu-view-months"></div></div>')

      for (var j = 0; j < 2; j++) {
        var $nav, instanceClass;

        if (j === 0) {
          $nav = $('<nav><div class="pmu-prev pmu-button"><i class="icon-arrow-left32"></i></div><div class="pmu-month pmu-button">\n              ' + (currentY - 1) + '\n              </div></nav>')
          instanceClass = "left-instance";
        } else {
          $nav = $('<nav><div class="pmu-month pmu-button">' + currentY + '</div><div class="pmu-next pmu-button"><i class="icon-arrow-right32"></i></div></nav>')
          instanceClass = "right-instance";
        }

        var $instance = $('<div class="pmu-instance ' + instanceClass + '"><div class="pmu-months"></div></div>')
        $instance.prepend($nav);

        for (var i = 0; i < months.length; i++) {
          var $m = $('<div class="pmu-button month-btn">' + months[i] + '</div>')
          $instance.find(".pmu-months").append($m);
        }

        $wrapper.find(".pmu-view-months").append($instance);
      }

      _picker = $modal.find(".modal-body").html($wrapper);
    } else {
      _picker = pickmeup('#datepicker_' + id, {
        locale: options.locale,
        locales: options.locales,
        flat: true,
        date: [startDate, endDate],
        mode: "range",
        calendars: 2,
        min: options.minDate,
        max: options.maxDate,
        format: options.format,
        prev: '<i class="icon-arrow-left32"></i>',
        next: '<i class="icon-arrow-right32"></i>',
        render: function render(date, d) {
          var dates = pickmeup('#datepicker_' + id).get_date(false)
          var class_name = [];

          if (!date || !dates.length) {
            return;
          }

          if (date.valueOf() === dates[0].valueOf()) {
            class_name.push("pmu-selected-first");
          }

          if (date.valueOf() === dates[dates.length - 1].valueOf()) {
            class_name.push("pmu-selected-last");
          }

          return {
            class_name: class_name.join(" ")
          };
        }
      });
      var element = document.getElementById('datepicker_' + id)
      element.addEventListener('pickmeup-change', function(e) {
        $modal.find("button").removeClass("active");
        $el.trigger("change");
      });
      $('#' + id).on('show show.bs.modal', function() {
        var defaultDates = $el.val() || ''

        var _defaultDates$split = defaultDates.split(' - '),
          startDate = _defaultDates$split[0],
          endDate = _defaultDates$split[1]

        if (!startDate || !endDate) {
          return
        }

        startDate = moment(startDate, options.format)
        endDate = moment(endDate, options.format)

        _picker.set_date([startDate.toDate(), endDate.toDate()])

        if (options.clockpicker) {
          $modal.find('.start_time').find('input').val(startDate.format('HH:mm'))
          $modal.find('.end_time').find('input').val(endDate.format('HH:mm'))
        }
      });
    }

    this._picker = _picker;
    this.$modal = $modal;
    this.$el = $el;
    this.isModal = isModal;
    this.options = options;

    if (isModal) {
      $el.on('click', function() {
        $(this).parents('.modal-body:not(.include-calendar-' + id + ')').hide()
        $(this).parents('.modal').find('.modal-footer:not(.include-calendar-' + id + ')').hide()
        $(this).parents('.modal').find('.modal-header').hide()
        $(this).parents('.modal').find('.modal-body.include-calendar-' + id).show()
        $(this).parents('.modal').find('.modal-footer.include-calendar-' + id).show()
      });
    } else {
      $el.attr('data-toggle', 'modal').attr('data-target', '#' + id)
    }

    this.buttonEvents($ranges);
    setTimeout(function() {
      if (isModal) {
        $el.parents('.modal').find('.dpicker-apply-btn').trigger('click')
      } else {
        $modal.find('.dpicker-apply-btn').trigger('click')
      }
    }, 225);

    if (options.clockpicker) {
      var checkValid = function checkValid() {
        var returnTrue = function returnTrue() {
          $modal.find('.modal-footer small').html('').hide()
          return true
        }

        if (!options.errorText) {
          return returnTrue()
        }

        var dates = this._picker.get_date(false)

        if (dates.length >= 2 && !moment(dates[0]).isSame(moment(dates[1]), 'day')) {
          return returnTrue()
        }

        var startTime = $modal.find('.start_time').find('input').val()
        var endTime = $modal.find('.end_time').find('input').val()
        var isAfter = moment(startTime, 'HH:mm').isAfter(moment(endTime, 'HH:mm'))

        if (!isAfter) {
          return returnTrue()
        }

        // finally set error message
        $modal.find('.modal-footer small').html(options.errorText).show()
        return false
      };

      this.checkValid = checkValid

      if (jQuery().clockpicker) {
        $modal.find(".start_time").clockpicker({
          placement: "top",
          autoclose: true,
          default: "00:00",
          afterDone: function afterDone(e, d) {
            var value = $modal.find('.start_time').find('input').val()
            $modal.find('.start_time').find('input').val(value)
            checkValid()
          }
        });
        $modal.find(".end_time").clockpicker({
          placement: "top",
          autoclose: true,
          default: "23:59",
          afterDone: function afterDone(e, d) {
            var value = $modal.find('.end_time').find('input').val()
            $modal.find('.end_time').find('input').val(value)
            checkValid()
          }
        });
      } else {
        console.warn("Please include Clockpicker plugin!");
      }
    }
  }

  DatePicker.prototype.buttonEvents = function($ranges) {
    var picker = this._picker
    var $modal = this.$modal
    var $el = this.$el
    var isModal = this.isModal
    var self = this

    if (self.options.hasAll) {
      $ranges.find('div.text-center').prepend('<button class="all btn btn-xs btn-link">All</button>')
    }

    if (self.options.customRange && self.options.hasRange) {
      var ranges = self.options.customRange;
      $ranges.find('button').on('click', function() {
        $ranges.find("button").removeClass("active");
        $(this).addClass('active').trigger('blur')

        for (var i = 0; i < ranges.length; i++) {
          var className = ranges[i].label.toLowerCase().split(' ').join('-');

          if ($(this).hasClass(className)) {
            picker.set_date(ranges[i].range);
          }
        }

        if ($(this).hasClass("all")) {
          picker.clear();
        }
      });
    } else if (self.options.hasRange) {
      $ranges.find('button').on('click', function() {
        $ranges.find("button").removeClass("active");
        $(this).addClass('active').trigger('blur')

        if ($(this).hasClass("today")) {
          picker.set_date([moment().toDate(), moment().toDate()]);
        } else if ($(this).hasClass("yesterday")) {
          var yesterday = moment().add(-1, "days");
          picker.set_date([yesterday.toDate(), yesterday.toDate()]);
        } else if ($(this).hasClass("term")) {
          picker.set_date([moment().toDate(), moment().add(+7, 'days').toDate()])
        } else if ($(this).hasClass("term-to-date")) {
          picker.set_date([moment().startOf('month').toDate(), moment().endOf('month').toDate()])
        } else if ($(this).hasClass("all")) {
          picker.clear();
        }
      });
    }

    if (isModal) {
      var id = self.options.id
      $el.parents('.modal').find('.dpicker-cancel-btn').on('click', function() {
        $el.parents('.modal').find('.modal-body.include-calendar-' + id).hide()
        $el.parents('.modal').find('.modal-footer.include-calendar-' + id).hide()
        $el.parents('.modal').find('.modal-body:not(.include-calendar-' + id + ', [class*=\'include-calendar-\'])').show()
        $el.parents('.modal').find('.modal-footer:not(.include-calendar-' + id + ', [class*=\'include-calendar-\'])').show()
        $el.parents('.modal').find('.modal-header').show()
      })
      $el.parents('.modal').find('.dpicker-apply-btn').on('click', function() {
        if (self.checkValid && !self.checkValid()) {
          return
        }

        self.setFieldValue()
        $el.parents('.modal').find('.dpicker-cancel-btn').trigger('click')
      })
    } else {
      $modal.find('.dpicker-cancel-btn').on('click', function() {
        $modal.modal("hide");
      });
      $modal.find('.dpicker-apply-btn').on('click', function() {
        if (self.checkValid && !self.checkValid()) {
          return
        }

        self.setFieldValue();
        $modal.find(".dpicker-cancel-btn").trigger("click");
      });
    }
  };

  DatePicker.prototype.setFieldValue = function() {
    if (!this._picker.get_date) {
      return;
    }

    var $el = this.$el;

    var dates = this._picker.get_date(false);

    if (!this.$modal || this.$modal.is(':hidden')) {
      return
    }

    var startTime = this.$modal.find(".start_time > input").val();
    var endTime = this.$modal.find(".end_time > input").val();
    var options = this.options;
    var result = "";

    if (dates.length >= 2) {
      if (options.clockpicker) {
        var startDate = moment(dates[0]).format('ll') + ' ' + startTime
        var endDate = moment(dates[1]).format('ll') + ' ' + endTime
        result += startDate + ' - ' + endDate
        dates = [startDate, endDate]
      } else {
        result += moment(dates[0]).format('ll') + ' - ' + moment(dates[1]).format('ll')
      }
    } else {
      result = "All";
    }

    if ($el.is("input")) {
      $el.val(result).trigger("change");
    } else {
      $el.children('div').addClass('pull-left').html('<span class="text-semibold">Date: </span> ' + result)
    }

    if (this.options && {}.toString.call(this.options.onDone) === '[object Function]') {
      this.options.onDone(dates);
    }
  };

  $.fn.calendar = function(options) {
    return this.each(function() {
      new DatePicker($(this), options);
    });
  };
})(jQuery);
