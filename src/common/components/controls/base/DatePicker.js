/* global $ */
import classnames from 'classnames';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React from 'react';

import translatePropTypes from '../../../translatePropTypes';
import {
  DatePickerDateFormat,
  TransportFormat,
  getFormattedAge,
} from '../../../utils/dateTime';
import makeCalendarLocale from '../../../utils/makeCalendarLocale';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import Icon from '../../utils/Icon';
import withTranslations from '../../utils/Translations/withTranslations';
import AnimatedTitle from './AnimatedTitle';

import styles from './DatePicker.scss';
import FormControlInfoMessage from '../../utils/FormControlInfoMessage';

@withTranslations
export default class DatePicker extends React.PureComponent {
  static propTypes = {
    value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    disabled: PropTypes.bool,
    animateTitle: PropTypes.bool,
    placeholder: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    required: PropTypes.bool,
    errorMessage: PropTypes.string,
    age: PropTypes.bool,
    isDurationNeeded: PropTypes.bool,
    isFutureDatesDisabled: PropTypes.bool,
    isPastDatesDisabled: PropTypes.bool,
    minDate: PropTypes.string,
    maxDate: PropTypes.string,
    hasLabel: PropTypes.bool,
    hasInlineLabel: PropTypes.bool,
    todayButtonType: PropTypes.oneOfType([
      PropTypes.oneOf(['linked']),
      PropTypes.bool,
    ]),
    isTodayButtonHighlighted: PropTypes.bool,
    isOutOfRangeValueVisible: PropTypes.bool,
    isInputEnabled: PropTypes.bool,
    ...translatePropTypes,
  };

  static defaultProps = {
    placeholder: '',
    animateTitle: true,
    disabled: false,
    value: '',
    required: false,
    errorMessage: null,
    age: false,
    isDurationNeeded: true,
    isFutureDatesDisabled: false,
    isPastDatesDisabled: false,
    minDate: null,
    maxDate: null,
    hasLabel: true,
    hasInlineLabel: false,
    todayButtonType: 'linked',
    isTodayButtonHighlighted: true,
    isOutOfRangeValueVisible: false,
    isInputEnabled: false,
  };

  state = {
    inputPlaceHolder: this.props.placeholder,
  };

  componentDidMount() {
    const {
      isFutureDatesDisabled,
      isPastDatesDisabled,
      todayButtonType,
      isTodayButtonHighlighted,
      onChange,
      minDate,
      maxDate,
      t,
      value,
      isOutOfRangeValueVisible,
      placeholder,
      isInputEnabled,
    } = this.props;

    const cellParams = isNegative => ({
      enabled: !isNegative,
      classes: isNegative && styles.invisible,
    });

    const beforeShowItem = date => {
      const firstYear = moment('0001-01-01 00:00');
      const isNegative = moment(date).isBefore(firstYear);
      return cellParams(isNegative);
    };

    this.$picker = $(this.datepicker);
    this.$picker.datepicker.dates.locale = makeCalendarLocale(t);

    const options = _.pickBy({
      startDate:
        (minDate && moment(minDate).toDate()) ||
        (isPastDatesDisabled && moment().toDate()),
      endDate:
        (maxDate && moment(maxDate).toDate()) ||
        (isFutureDatesDisabled && moment().toDate()),
      format: DatePickerDateFormat,
      todayHighlight: isTodayButtonHighlighted,
      todayBtn: todayButtonType,
      beforeShowYear: beforeShowItem,
      beforeShowDecade: beforeShowItem,
      beforeShowCentury: beforeShowItem,
      language: 'locale',
      pickerClass: classnames(styles.datePicker),
      startView: value ? 'days' : 'years',
    });

    this.$picker
      .datepicker(options)
      .on('changeDate', ({ date }) => {
        onChange(date ? moment(date).format(TransportFormat) : null);
        this.$picker.datepicker('hide');
      })
      .on(
        'hide',
        () =>
          isInputEnabled && this.setState({ inputPlaceHolder: placeholder }),
      );

    if (value) {
      const pickerSpecificValue = moment(value, TransportFormat).format(
        'll',
      );
      this.$picker.datepicker('update', pickerSpecificValue);
      //workaround to keep date value if date is out of min-max range
      isOutOfRangeValueVisible && this.$picker.val(pickerSpecificValue);
    }
  }

  componentDidUpdate(prevProps) {
    const { currentLanguage, t, value } = this.props;
    const { currentLanguage: prevLanguage } = prevProps;

    if (value) {
      this.$picker.datepicker('switchView', 'days');
    } else {
      this.$picker.datepicker('switchView', 'years');
    }

    if (currentLanguage !== prevLanguage) {
      //https://github.com/uxsolutions/bootstrap-datepicker/issues/362
      this.$picker.datepicker.dates.locale = makeCalendarLocale(t);
      this.$picker.datepicker('update');

      this.$picker.data().datepicker.picker.find('tr:nth(2)').remove();
      this.$picker.datepicker('fillDow');
    }
  }

  componentWillUnmount() {
    this.$picker.datepicker('destroy');
  }

  renderDuration = date => {
    const { age, t } = this.props;

    const duration = age
      ? getFormattedAge(date.toISOString(), t, { isIncludeMonth: true })
      : moment.duration(date.diff(moment()), 'milliseconds').humanize(!age);

    const className = classnames(
      'help-block text-right',
      styles.ageCalculation,
    );

    return <FormControlInfoMessage message={duration} messageAlign="right" />;
  };

  renderAnimatedTitle = () => {
    const { value, placeholder, required } = this.props;
    return (
      <AnimatedTitle
        isRequired={required}
        placeholder={placeholder}
        value={value}
      />
    );
  };

  renderErrorMessage = () => {
    const { errorMessage } = this.props;
    return <FormControlErrorMessage errorMessage={errorMessage} />;
  };

  handleDatePickerIconClick = () => $(this.datepicker).datepicker('show');

  handleFocus = () => {
    const { isInputEnabled } = this.props;
    isInputEnabled && this.setState({ inputPlaceHolder: 'DD/MM/YYYY' });
  };

  render() {
    const {
      placeholder,
      animateTitle,
      value,
      disabled,
      onChange,
      required,
      errorMessage,
      isDurationNeeded,
      hasLabel,
      hasInlineLabel,
      isInputEnabled,
    } = this.props;

    const { inputPlaceHolder } = this.state;
    const date = value ? moment(value, TransportFormat) : null;
    return (
      <div className="form-group form-group-material">
        {hasLabel && animateTitle && this.renderAnimatedTitle()}
        <div className={classnames({ 'input-group': !disabled })}>
          {hasInlineLabel && (
            <span
              className={classnames('datepicker-label', styles['inline-label'])}
            >
              {placeholder}:
            </span>
          )}
          <input
            ref={e => (this.datepicker = e)}
            className={classnames(
              'form-control daterange-single date-of-birth',
              { [styles.disabledText]: disabled },
              {
                [styles.inputDisabled]: !disabled && !isInputEnabled,
              },
            )}
            disabled={disabled}
            placeholder={required ? `${inputPlaceHolder} *` : inputPlaceHolder}
            readOnly={!isInputEnabled}
            onChange={onChange}
            onFocus={this.handleFocus}
          />
          {!disabled && !hasInlineLabel && (
            <span
              className="input-group-addon cursor-pointer"
              onClick={this.handleDatePickerIconClick}
            >
              <Icon name="calendar2" />
            </span>
          )}
        </div>
        {isDurationNeeded && value && this.renderDuration(date)}
        {errorMessage && this.renderErrorMessage()}
      </div>
    );
  }
}
