/* global $ */
import classnames from 'classnames';
import { isEmpty, pickBy } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React from 'react';
import translatePropTypes from '../../../translatePropTypes';
import {
  MomentDateTimeFormat,
  MomentDateTimeFriendlyFormat,
  TimeFormat,
  TransportDateTimeFormat,
  TransportFormat,
} from '../../../utils/dateTime';
import makeCalendarLocale from '../../../utils/makeCalendarLocale';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import Icon from '../../utils/Icon';
import Modal from '../../utils/Modal/Modal';

import withTranslations from '../../utils/Translations/withTranslations';
import Button from '../Button';
import RoundedPrimaryButton from '../RoundedPrimaryButton';
import AnimatedTitle from './AnimatedTitle';

import styles from './DatePicker.scss';

const INVALID_DATE = 'Invalid date';
@withTranslations
export default class DateTimePicker extends React.PureComponent {
  static propTypes = {
    value: PropTypes.oneOfType([
      PropTypes.number,
      PropTypes.string,
      PropTypes.any,
    ]),
    name: PropTypes.string,
    timeZone: PropTypes.string,
    animateTitle: PropTypes.bool,
    disabled: PropTypes.bool,
    placeholder: PropTypes.string,
    required: PropTypes.bool,
    errorMessage: PropTypes.string,
    className: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    age: PropTypes.bool,
    isDurationNeeded: PropTypes.bool,
    isFutureDatesDisabled: PropTypes.bool,
    isPastDatesDisabled: PropTypes.bool,
    hasLabel: PropTypes.bool,
    hasInlineLabel: PropTypes.bool,
    todayButtonType: PropTypes.oneOfType([
      PropTypes.oneOf(['linked']),
      PropTypes.bool,
    ]),
    isTodayButtonHighlighted: PropTypes.bool,
    isLinkStyle: PropTypes.bool,
    showIcon: PropTypes.bool,
    isInPage: PropTypes.bool,
    onCancel: PropTypes.func,
    isTimeRequired: PropTypes.bool,
    isReadOnly: PropTypes.bool,
    isEmptyAllowed: PropTypes.bool,
    onClear: PropTypes.func,
    dateTimeFormat: PropTypes.string,
    ...translatePropTypes,
  };

  static defaultProps = {
    name: null,
    animateTitle: true,
    timeZone: '',
    placeholder: '',
    disabled: false,
    required: false,
    errorMessage: null,
    className: '',
    value: '',
    age: false,
    isDurationNeeded: true,
    isFutureDatesDisabled: false,
    isPastDatesDisabled: false,
    hasLabel: true,
    hasInlineLabel: false,
    todayButtonType: 'linked',
    isTodayButtonHighlighted: true,
    isLinkStyle: false,
    showIcon: true,
    isInPage: false,
    onCancel: null,
    isTimeRequired: true,
    isReadOnly: true,
    isEmptyAllowed: false,
    onClear: null,
    dateTimeFormat: null,
  };

  constructor(props) {
    super(props);

    this.state = this.getStateFromProps(props);

    this.dateInput = null;
    this.timeInput = null;
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (
      prevState.date &&
      prevState.time &&
      nextProps.value === null &&
      nextProps.value !== prevState.value
    ) {
      return {
        ...prevState,
        value: nextProps.value,
        date: null,
        time: null,
      };
    }

    if (nextProps.value && nextProps.value !== prevState.value) {
      const valueMoment = moment(nextProps.value);
      return {
        ...prevState,
        date: valueMoment.format(TransportFormat),
        time: valueMoment.format(TimeFormat),
        value: nextProps.value,
      };
    }
    return null;
  }

  componentDidUpdate({ currentLanguage: prevLanguage, value: prevValue }) {
    this.timeZone = this.props.timeZone;
    moment.tz.setDefault(this.timeZone);
    const { currentLanguage, t, value } = this.props;

    if (currentLanguage !== prevLanguage) {
      const dateTimePickerElement = this.dateInput;
      //https://github.com/uxsolutions/bootstrap-datepicker/issues/362
      if (dateTimePickerElement) {
        dateTimePickerElement.datepicker.dates.locale = makeCalendarLocale(t);
        dateTimePickerElement.datepicker('update');

        const data = dateTimePickerElement.data();
        if (data) {
          data.datepicker.picker.find('tr:nth(2)').remove();
          dateTimePickerElement.datepicker('fillDow');
        }
      }
    }
  }

  componentWillUnmount() {
    this.dateInput && this.dateInput.datepicker('destroy');
    this.timeInput && this.timeInput.clockpickerInline('destroy');
  }
  timeZone = '';
  getStateFromProps = props => {
    const { value } = props;

    if (value) {
      const valueMoment = moment(value);
      return {
        isModalVisible: false,
        date: valueMoment.format(TransportFormat),
        time: valueMoment.format(TimeFormat),
        value: props.value,
      };
    }
    return {
      isModalVisible: false,
      date: null,
      time: null,
      value: props.value,
    };
  };

  wrapDateDiv = e => {
    this.dateInput = $(e);

    const {
      isFutureDatesDisabled,
      isPastDatesDisabled,
      todayButtonType,
      isTodayButtonHighlighted,
      t,
    } = this.props;

    const cellParams = isNegative => ({
      enabled: !isNegative,
      classes: isNegative && styles.invisible,
    });

    const beforeShowItem = date => {
      const firstYear = moment('0001-01-01 00:00');
      const isNegative = moment(date).isBefore(firstYear);
      return cellParams(isNegative);
    };

    this.dateInput.datepicker.dates.locale = makeCalendarLocale(t);

    const options = pickBy({
      endDate: isFutureDatesDisabled && moment().toDate(),
      startDate: isPastDatesDisabled && moment().toDate(),
      format: TransportFormat,
      todayHighlight: isTodayButtonHighlighted,
      todayBtn: todayButtonType,
      beforeShowYear: beforeShowItem,
      beforeShowDecade: beforeShowItem,
      beforeShowCentury: beforeShowItem,
      language: 'locale',
    });

    this.dateInput.datepicker(options).on('changeDate', e => {
      const { date } = e;
      const { time } = this.state;
      const currentDate = moment().format(MomentDateTimeFriendlyFormat);
      const _date = moment(date).add(time).format(MomentDateTimeFriendlyFormat);
      const todayIsSelected = moment(_date).isSame(currentDate, 'day');
      const isPastTimeDisabled = isPastDatesDisabled && todayIsSelected;

      if (isPastTimeDisabled && moment(_date).isBefore(currentDate)) {
        this.setState({ time: moment(currentDate).format(TimeFormat) });
      }

      this.setState({ date: _date, time: time || '00:00' });
      this.dateInput.datepicker('hide');
      this.timeInput.clockpickerInline('reset', isPastTimeDisabled);
    });

    const { date } = this.state;

    this.dateInput.datepicker(
      'update',
      (date ? moment(date) : moment()).toDate(),
    );
    this.dateInput.find('th.today').html(this.props.t('Today'));
  };

  wrapTimeInput = e => {
    this.timeInput = $(e);
    const { date } = this.state;
    const self = this;

    const { value, isPastDatesDisabled, t } = this.props;

    let defaultTime = value ? moment(value).format(TimeFormat) : '00:00';

    if (
      isPastDatesDisabled &&
      (isEmpty(value) || moment().isAfter(moment(value)))
    ) {
      defaultTime = 'now';
    }

    const dateSelected = date && date !== INVALID_DATE ? date : moment.now();
    const todayIsSelected = moment(dateSelected).isSame(new Date(), 'day');

    this.timeInput.clockpickerInline({
      default: defaultTime,
      label: t('Time'),
      isPastTimeDisabled:
        isPastDatesDisabled &&
        (todayIsSelected || moment().isAfter(moment(value))),
      afterDone() {
        self.setState({ time: self.timeInput.val() });
      },
    });

    this.timeInput.clockpickerInline('show');
  };

  renderAnimatedTitle = () => {
    const { placeholder, required, value } = this.props;
    const { _isValid } = value;
    const placeholderValue = !_isValid ? '' : placeholder;
    return (
      <AnimatedTitle
        isRequired={_isValid ? required : undefined}
        placeholder={placeholderValue}
        value={placeholder && value}
      />
    );
  };

  renderErrorMessage = () => {
    const { errorMessage } = this.props;
    return <FormControlErrorMessage errorMessage={errorMessage} />;
  };

  handleOpenPicker = () => {
    this.setState({ isModalVisible: true });
  };

  handleModalClose = () => {
    this.setState(this.getStateFromProps(this.props));
  };

  handleModalApply = () => {
    this.setState({ isModalVisible: false });
    const {
      onChange,
      isEmptyAllowed,
      isPastDatesDisabled,
      dateTimeFormat,
    } = this.props;
    const { time, date } = this.state;

    let timeDate;
    if (!date && !time && isEmptyAllowed) {
      timeDate = null;
    } else if (
      date &&
      time &&
      isPastDatesDisabled &&
      moment(date).isSameOrBefore(moment(), 'day') &&
      moment(time).isSameOrBefore(moment(), 'time')
    ) {
      if (
        moment(`${moment().format('ll')} ${time}`).isSameOrBefore(
          moment(),
          'minute',
        )
      ) {
        timeDate = moment().format(dateTimeFormat || MomentDateTimeFormat);
      } else {
        timeDate = dateTimeFormat
          ? moment().format(dateTimeFormat)
          : `${moment().format('ll')} ${time}`;
      }
    } else {
      timeDate = moment(this.value, MomentDateTimeFormat).format(
        TransportDateTimeFormat,
      );
    }
    onChange(timeDate);

    if (timeDate !== INVALID_DATE) {
      onChange(timeDate);
    } else {
      onChange('');
    }
  };

  get value() {
    const { isTimeRequired, value } = this.props;
    const { time, date } = this.state;
    const dateFormats = [MomentDateTimeFormat, TransportFormat];

    if (!time && !date && value) {
      const valueMoment = moment(value);
      const dateProp = valueMoment.format(TransportFormat);
      const timeProps =
        !isTimeRequired && !valueMoment.format(TimeFormat)
          ? '00:00'
          : valueMoment.format(TimeFormat);

      return dateProp && timeProps
        ? `${moment(dateProp).format('ll')} ${timeProps}`
        : '';
    }
    const timeValue = !isTimeRequired && !time ? '00:00' : time;

    if (date && date !== INVALID_DATE && time) {
      return `${moment(date, dateFormats).format(
        'll',
      )} ${timeValue}`;
    }

    if ((!date || date === INVALID_DATE) && time) {
      return `${moment().format('ll')} ${timeValue}`;
    }

    if (date && !time) {
      if (!timeValue) {
        return `${moment(date).format('ll')} ${moment().format(
          TimeFormat,
        )}`;
      }
      return `${moment(date).format('ll')} ${timeValue}`;
    }

    return '';
  }

  handleClear = () => {
    const { isEmptyAllowed, isPastDatesDisabled } = this.props;
    let time = null;
    let date = null;

    if (!isEmptyAllowed) {
      time = moment().format(TimeFormat);
      date = moment().format(TransportFormat);
    }

    this.setState({ time, date });
    const { onChange } = this.props;

    this.dateInput.datepicker('update', date);
    this.timeInput.clockpickerInline(
      'reset',
      isPastDatesDisabled,
      true,
      isEmptyAllowed ? '00:00' : time,
    );
    this.timeInput.prop('value', time);
    onChange && onChange('');
  };

  renderModalFooter = () => {
    const {
      isInPage,
      isTimeRequired,
      t,
      onCancel,
      isEmptyAllowed,
      isPastDatesDisabled,
    } = this.props;
    const { date, time } = this.state;
    const disabled =
      isPastDatesDisabled &&
      moment(`${moment(date).format('ll')} ${time}`).isBefore(
        moment(),
        'minute',
      );

    return (
      <>
        <Button
          additionClasses={classnames(styles.smallButton, {
            [styles.clearButton]: isInPage,
          })}
          buttonStyle="link"
          onClick={this.handleClear}
        >
          {t('Clear')}
        </Button>

        <Button
          additionClasses={classnames('ml-5', styles.smallButton, {
            [styles.cancelButton]: isInPage,
          })}
          buttonStyle="link"
          onClick={isInPage ? onCancel : this.handleModalClose}
        >
          {t('Cancel')}
        </Button>

        <RoundedPrimaryButton
          additionClasses={styles.smallButton}
          disabled={disabled}
          onClick={this.handleModalApply}
        >
          {t('Apply')}
        </RoundedPrimaryButton>
      </>
    );
  };

  renderDuration = () => {
    const { age, value } = this.props;

    const duration = moment
      .duration(moment(value).diff(moment()), 'milliseconds')
      .humanize(!age);

    const className = classnames(
      'help-block text-right',
      styles.ageCalculation,
    );

    return (
      <span className={className}>
        {duration === INVALID_DATE ? '' : duration}
      </span>
    );
  };

  render() {
    const {
      className,
      errorMessage,
      name,
      animateTitle,
      placeholder,
      required,
      disabled,
      value,
      isDurationNeeded,
      hasLabel,
      hasInlineLabel,
      isLinkStyle,
      showIcon,
      isInPage,
      isReadOnly,
    } = this.props;

    const { isModalVisible, time } = this.state;

    return (
      <div className={classnames('form-group form-group-material', className)}>
        {hasLabel && animateTitle && this.renderAnimatedTitle()}
        <div className={classnames({ 'input-group': !disabled })}>
          {hasInlineLabel && (
            <span
              className={classnames('datepicker-label', styles['inline-label'])}
            >
              {placeholder}:
            </span>
          )}
          <input
            className={classnames('form-control', {
              [styles.solidBorder]: !disabled,
            })}
            disabled={disabled}
            name={name}
            placeholder={`${placeholder}${required ? ' *' : ''}`}
            readOnly={isReadOnly}
            value={moment(value).isValid() ? this.value : ''}
            onFocus={this.handleOpenPicker}
          />
          {!(disabled || hasInlineLabel) && showIcon && (
            <span
              className={classnames('input-group-addon cursor-pointer', {
                [styles.linkStyle]: isLinkStyle,
              })}
              onClick={this.handleOpenPicker}
            >
              <Icon name="calendar2" />
            </span>
          )}
          {isInPage ? (
            <>
              <div
                className={classnames('row text-center', {
                  [styles.clockPickerInPageBorder]: isInPage,
                })}
              >
                <div
                  ref={this.wrapDateDiv}
                  className="clockpicker-popover inline"
                />
                <input
                  ref={this.wrapTimeInput}
                  className="hidden"
                  value={time}
                />
              </div>
              <div
                className={classnames('pull-right', {
                  row: !isInPage,
                })}
              >
                {this.renderModalFooter()}
              </div>
            </>
          ) : (
            <Modal
              renderFooter={this.renderModalFooter}
              visible={isModalVisible}
            >
              <div className="row text-center">
                <div
                  ref={this.wrapDateDiv}
                  className="clockpicker-popover inline"
                />
                <input
                  ref={this.wrapTimeInput}
                  className="hidden"
                  value={time}
                />
              </div>
            </Modal>
          )}
        </div>
        {isDurationNeeded && !errorMessage && value && this.renderDuration()}
        {errorMessage && this.renderErrorMessage()}
      </div>
    );
  }
}
